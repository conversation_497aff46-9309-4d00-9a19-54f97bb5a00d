# Postman Collection Update: deleteOriginal Feature

## Overview
Updated the Postman collection (`postman_collection.json`) to include the new `deleteOriginal` parameter for compression endpoints.

## Changes Made

### 1. Collection Description
- Added information about the new `deleteOriginal` feature
- Explained safety features and usage guidelines
- Updated feature list to highlight the new functionality

### 2. Compress File Upload Endpoint
**Updated Parameters:**
- Added `deleteOriginal` parameter (disabled by default)
- Updated description to explain the parameter is ignored for uploads
- Enhanced response examples to show `original_deleted` field

**Form Data Addition:**
```json
{
  "key": "deleteOriginal",
  "value": "false",
  "description": "Delete original files after successful compression",
  "type": "text",
  "disabled": true
}
```

### 3. Compress Local Path Endpoint
**Updated Parameters:**
- Added `deleteOriginal` parameter (disabled by default)
- Enhanced description with detailed explanation of the feature
- Added examples showing both success and error scenarios

**Form Data Addition:**
```json
{
  "key": "deleteOriginal",
  "value": "false",
  "description": "Delete original file/directory after successful compression",
  "disabled": true
}
```

### 4. New Test Endpoints

#### A. "Compress with Delete Original (True)"
- **Purpose**: Test compression with `deleteOriginal=true`
- **Warning**: Includes safety warning about permanent deletion
- **Tests**: Validates `original_deleted` field in response
- **Response Examples**: Shows both success and error scenarios

#### B. "Compress with Delete Original (False)"
- **Purpose**: Test compression with `deleteOriginal=false` (default)
- **Tests**: Validates that `original_deleted` field is not present
- **Response Examples**: Shows standard compression response

## Updated Response Examples

### Success with Deletion
```json
{
  "status": "success",
  "output_path": "/tmp/compression_api/outputs/file.zmt",
  "original_size": "180.0MB",
  "compressed_size": "70.0MB",
  "compression_ratio": "61.1%",
  "original_size_bytes": 188743680,
  "compressed_size_bytes": 73400320,
  "original_deleted": true
}
```

### Success with Deletion Error
```json
{
  "status": "success",
  "output_path": "/tmp/compression_api/outputs/file.zmt",
  "original_size": "180.0MB",
  "compressed_size": "70.0MB",
  "compression_ratio": "61.1%",
  "original_deleted": false,
  "deletion_error": "Permission denied"
}
```

## How to Use in Postman

### 1. Import Updated Collection
1. Open Postman
2. Click "Import"
3. Select the updated `postman_collection.json` file
4. The collection will include the new `deleteOriginal` parameter

### 2. Test Delete Original Feature

#### Safe Testing (Recommended):
1. Use "Compress with Delete Original (False)" first
2. Verify the file is compressed and original is preserved
3. Create a test file you don't need
4. Use "Compress with Delete Original (True)" with the test file

#### Parameters to Enable:
- In any compression request, enable the `deleteOriginal` parameter
- Set value to `true` to delete original
- Set value to `false` or leave disabled to keep original

### 3. Test Scenarios

#### Scenario 1: Basic Delete Original
```
POST /api/compress
Content-Type: application/x-www-form-urlencoded

input_path=/path/to/test-file.txt
deleteOriginal=true
```

#### Scenario 2: Delete Original with Custom Output
```
POST /api/compress
Content-Type: application/x-www-form-urlencoded

input_path=/path/to/test-file.txt
output_filename=custom.zmt
deleteOriginal=true
maxCompress=true
```

#### Scenario 3: Keep Original (Default)
```
POST /api/compress
Content-Type: application/x-www-form-urlencoded

input_path=/path/to/file.txt
deleteOriginal=false
```

## Safety Features in Postman Collection

1. **Parameters Disabled by Default**: The `deleteOriginal` parameter is disabled by default to prevent accidental deletion
2. **Clear Warnings**: Test requests include warnings about permanent deletion
3. **Separate Test Endpoints**: Dedicated endpoints for testing delete functionality
4. **Comprehensive Tests**: Postman tests validate the response structure and deletion status

## Testing the Collection

### Prerequisites
1. API server running on `http://localhost:8000`
2. Test files available for compression
3. Appropriate file permissions for deletion tests

### Test Sequence
1. **Health Check**: Verify API is running
2. **Compress Local Path (Keep Original)**: Test default behavior
3. **Compress with Delete Original (False)**: Test explicit false
4. **Compress with Delete Original (True)**: Test deletion (use test files only!)

### Expected Results
- All tests should pass
- `original_deleted` field should reflect actual deletion status
- Compression should succeed even if deletion fails
- Original files should only be deleted when `deleteOriginal=true`

## Files Updated
- `postman_collection.json` - Main collection file with new parameters and endpoints

## Validation
✅ JSON syntax validated
✅ All endpoints include `deleteOriginal` parameter
✅ Comprehensive documentation added
✅ Safety warnings included
✅ Test cases cover all scenarios

**The Postman collection is now ready to test the deleteOriginal functionality like a boss! 🚀**
