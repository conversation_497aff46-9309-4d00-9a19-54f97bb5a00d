# ZMT Compression API - Postman Documentation

## 📋 Overview

This Postman collection provides comprehensive testing and documentation for the ZMT Compression API. The collection includes all endpoints with examples, automated tests, and proper error handling scenarios.

## 🚀 Quick Start

### 1. Import Collection and Environment

1. **Import Collection:**
   - Open Postman
   - Click "Import" → "Upload Files"
   - Select `postman_collection.json`

2. **Import Environment:**
   - Click "Import" → "Upload Files" 
   - Select `postman_environment.json`
   - Set as active environment

### 2. Start the API Server

```bash
cd /path/to/compression-api
python3 main.py
```

The API will be available at `http://localhost:8000`

### 3. Run the Collection

- Click "Collections" → "ZMT Compression API"
- Click "Run" to execute all requests
- Or run individual requests as needed

## 📁 Collection Structure

### 🏥 Health Check
- **Get API Status** - Verify the API is running

### 🗜️ Compression
- **Compress File Upload** - Upload files for compression
- **Compress Local Path** - Compress files/directories by path

### 📦 Decompression  
- **Decompress Archive Upload** - Upload and extract ZMT archives
- **Decompress Local Archive** - Extract archives by path

### 📋 Archive Management
- **List Archive Contents** - Get archive information

## 🔧 Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `base_url` | API base URL | `http://localhost:8000` |
| `last_compressed_file` | Auto-populated by tests | `/tmp/compression_api/outputs/file.zmt` |
| `sample_file_path` | Test file path | `/Users/<USER>/Documents/sample.txt` |
| `sample_directory_path` | Test directory path | `/Users/<USER>/Documents/folder` |
| `custom_output_dir` | Custom extraction directory | `/tmp/my_extractions` |

## 📝 Request Examples

### Compress File Upload

```http
POST /api/compress
Content-Type: multipart/form-data

files: [file upload]
output_filename: "my_archive.zmt" (optional)
```

### Compress Local Path

```http
POST /api/compress
Content-Type: application/x-www-form-urlencoded

input_path=/path/to/file.txt
output_filename=custom_name.zmt
```

### Decompress Archive

```http
POST /api/uncompress
Content-Type: application/x-www-form-urlencoded

archive_path=/tmp/compression_api/outputs/file.zmt
output_dir=/custom/output/path
```

## 🧪 Automated Tests

Each request includes automated tests that verify:

- ✅ Correct HTTP status codes
- ✅ Response structure validation
- ✅ Required fields presence
- ✅ Data type validation
- ✅ Environment variable population

### Test Results Example:
```
✓ Status code is 200
✓ Response has compression data
✓ Output path is valid
✓ Compression ratio is calculated
```

## 🚨 Error Scenarios

The collection includes tests for common error scenarios:

| Status Code | Scenario | Example |
|-------------|----------|---------|
| 400 | Bad Request | Missing required parameters |
| 404 | Not Found | File/archive doesn't exist |
| 413 | Payload Too Large | File exceeds size limits |
| 422 | Validation Error | Invalid file format |
| 500 | Server Error | Compression/decompression failure |

## 📊 Response Examples

### Successful Compression
```json
{
  "status": "success",
  "output_path": "/tmp/compression_api/outputs/file.zmt",
  "original_size": "1.0MB",
  "compressed_size": "1.1KB", 
  "compression_ratio": "99.9%",
  "original_size_bytes": 1048576,
  "compressed_size_bytes": 1127
}
```

### Successful Decompression
```json
{
  "status": "success",
  "restored_path": "/tmp/compression_api/outputs/restored/file",
  "extracted_files": 3,
  "total_size": "1.0MB",
  "total_size_bytes": 1048576
}
```

### Error Response
```json
{
  "detail": "Input path not found: /nonexistent/file.txt"
}
```

## 🔄 Workflow Testing

### Complete Compression/Decompression Workflow:

1. **Health Check** - Verify API is running
2. **Compress File** - Upload or specify file for compression
3. **List Archive Contents** - Inspect the created archive
4. **Decompress Archive** - Extract the compressed file
5. **Verify Results** - Check extracted files

The collection automatically chains these requests using environment variables.

## 🛠️ Customization

### Adding Custom Tests

Add test scripts in the "Tests" tab of any request:

```javascript
pm.test("Custom validation", function () {
    const jsonData = pm.response.json();
    pm.expect(jsonData.compression_ratio).to.match(/^\d+\.\d+%$/);
});
```

### Environment Setup

Update environment variables for your specific setup:

```json
{
  "base_url": "https://your-api-domain.com",
  "sample_file_path": "/your/test/file.txt"
}
```

## 📈 Performance Testing

Use Postman's Collection Runner for performance testing:

1. Set iterations (e.g., 100 runs)
2. Add delays between requests
3. Monitor response times
4. Check for memory leaks or performance degradation

## 🔍 Debugging

### Common Issues:

1. **Connection Refused**: Ensure API server is running
2. **File Not Found**: Check file paths in environment variables
3. **Permission Denied**: Verify file system permissions
4. **Large File Timeouts**: Increase request timeout in Postman

### Debug Tips:

- Use Postman Console to view detailed request/response logs
- Check environment variable values
- Verify file paths exist before testing
- Monitor API server logs for detailed error messages

## 📚 Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [ZMT Compression Algorithm](https://github.com/zpaq/zpaq)
- [Postman Learning Center](https://learning.postman.com/)

---

**Ready to test your ZMT Compression API like a boss!** 🚀
