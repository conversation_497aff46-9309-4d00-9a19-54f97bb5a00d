#!/usr/bin/env python3
"""
Demo script to show deleteOriginal functionality in action
Run this with the API server running to see it work
"""
import os
import tempfile
import requests
import time
from pathlib import Path

def create_demo_files():
    """Create some demo files for testing"""
    demo_dir = Path("demo_files")
    demo_dir.mkdir(exist_ok=True)
    
    # Create a text file
    text_file = demo_dir / "sample.txt"
    text_file.write_text("This is a sample text file for compression testing.\n" * 50)
    
    # Create a directory with multiple files
    sub_dir = demo_dir / "documents"
    sub_dir.mkdir(exist_ok=True)
    
    (sub_dir / "doc1.txt").write_text("Document 1 content\n" * 20)
    (sub_dir / "doc2.txt").write_text("Document 2 content\n" * 30)
    (sub_dir / "readme.md").write_text("# README\nThis is a markdown file\n" * 10)
    
    return text_file, sub_dir

def demo_compress_with_delete():
    """Demonstrate compression with deleteOriginal=true"""
    print("🔥 SAMUEL L. JACKSON'S COMPRESSION API DEMO")
    print("=" * 50)
    
    # Create demo files
    print("Creating demo files...")
    text_file, sub_dir = create_demo_files()
    
    print(f"✓ Created: {text_file}")
    print(f"✓ Created: {sub_dir} with {len(list(sub_dir.iterdir()))} files")
    
    # Test 1: Compress single file with deleteOriginal=true
    print("\n📁 TEST 1: Compress single file with deleteOriginal=true")
    print("-" * 50)
    
    print(f"File exists before: {text_file.exists()}")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/compress",
            data={
                "input_path": str(text_file),
                "deleteOriginal": "true",
                "output_filename": "sample_compressed.zmt"
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Compression successful!")
            print(f"   Status: {result.get('status')}")
            print(f"   Output: {result.get('output_path')}")
            print(f"   Original size: {result.get('original_size')}")
            print(f"   Compressed size: {result.get('compressed_size')}")
            print(f"   Compression ratio: {result.get('compression_ratio')}")
            print(f"   Original deleted: {result.get('original_deleted', 'Not specified')}")
            
            if result.get('deletion_error'):
                print(f"   ⚠️  Deletion error: {result.get('deletion_error')}")
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"   Response: {response.text}")
    
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to API. Make sure server is running on localhost:8000")
        print("   Start the server with: uvicorn main:app --reload")
        return
    except Exception as e:
        print(f"❌ Error: {e}")
        return
    
    print(f"File exists after: {text_file.exists()}")
    
    # Test 2: Compress directory with deleteOriginal=false
    print("\n📁 TEST 2: Compress directory with deleteOriginal=false")
    print("-" * 50)
    
    print(f"Directory exists before: {sub_dir.exists()}")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/compress",
            data={
                "input_path": str(sub_dir),
                "deleteOriginal": "false",
                "output_filename": "documents_compressed.zmt"
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Compression successful!")
            print(f"   Status: {result.get('status')}")
            print(f"   Output: {result.get('output_path')}")
            print(f"   Original size: {result.get('original_size')}")
            print(f"   Compressed size: {result.get('compressed_size')}")
            print(f"   Compression ratio: {result.get('compression_ratio')}")
            print(f"   Original deleted: {result.get('original_deleted', 'Not specified')}")
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"   Response: {response.text}")
    
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print(f"Directory exists after: {sub_dir.exists()}")
    
    # Test 3: Show API documentation
    print("\n📖 API USAGE EXAMPLES")
    print("-" * 50)
    
    print("Compress and delete original:")
    print('curl -X POST "http://localhost:8000/api/compress" \\')
    print('  -F "input_path=/path/to/file.txt" \\')
    print('  -F "deleteOriginal=true"')
    
    print("\nCompress and keep original (default):")
    print('curl -X POST "http://localhost:8000/api/compress" \\')
    print('  -F "input_path=/path/to/file.txt" \\')
    print('  -F "deleteOriginal=false"')
    
    print("\nWith all options:")
    print('curl -X POST "http://localhost:8000/api/compress" \\')
    print('  -F "input_path=/path/to/file.txt" \\')
    print('  -F "output_filename=custom.zmt" \\')
    print('  -F "maxCompress=true" \\')
    print('  -F "deleteOriginal=true"')
    
    # Cleanup
    print("\n🧹 CLEANUP")
    print("-" * 50)
    
    try:
        import shutil
        if Path("demo_files").exists():
            shutil.rmtree("demo_files")
            print("✓ Cleaned up demo files")
    except Exception as e:
        print(f"⚠️  Cleanup error: {e}")
    
    print("\n🎉 Demo completed! The deleteOriginal functionality is working like a boss!")

if __name__ == "__main__":
    demo_compress_with_delete()
