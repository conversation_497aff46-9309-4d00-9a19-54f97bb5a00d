#!/usr/bin/env python3
"""
Test script to verify the ZMT exit code fix
"""
import tempfile
import requests
import os
from pathlib import Path

def test_compression_fix():
    """Test that compression works despite ZMT's non-zero exit codes"""
    
    print("🔧 Testing ZMT Exit Code Fix")
    print("=" * 50)
    
    # Create a test file
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
        test_file_path = f.name
        f.write("This is a test file to verify the ZMT exit code fix.\n" * 100)
    
    print(f"Created test file: {test_file_path}")
    print(f"File size: {os.path.getsize(test_file_path)} bytes")
    
    try:
        # Test compression via API
        print("\n🚀 Testing compression via API...")
        
        response = requests.post(
            "http://localhost:8000/api/compress",
            data={
                "input_path": test_file_path,
                "output_filename": "zmt_fix_test.zmt",
                "deleteOriginal": "false"
            },
            timeout=60
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS! Compression completed without errors")
            print(f"   Status: {result.get('status')}")
            print(f"   Output: {result.get('output_path')}")
            print(f"   Original size: {result.get('original_size')}")
            print(f"   Compressed size: {result.get('compressed_size')}")
            print(f"   Compression ratio: {result.get('compression_ratio')}")
            
            # Verify output file exists
            output_path = result.get('output_path')
            if output_path and os.path.exists(output_path):
                print(f"✅ Output file verified: {output_path}")
                print(f"   Output file size: {os.path.getsize(output_path)} bytes")
            else:
                print("❌ Output file not found!")
                
        elif response.status_code == 500:
            print("❌ FAILED! Still getting 500 error")
            print(f"   Response: {response.text}")
            
            # Try to get more details
            try:
                error_detail = response.json()
                print(f"   Error detail: {error_detail.get('detail', 'No detail available')}")
            except:
                pass
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to API. Make sure server is running:")
        print("   uvicorn main:app --reload")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        # Clean up test file
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
            print(f"\n🧹 Cleaned up test file: {test_file_path}")
    
    return response.status_code == 200

def test_direct_script():
    """Test the zmt.sh script directly"""
    print("\n🔧 Testing zmt.sh script directly...")
    print("-" * 50)
    
    # Create a test file
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
        test_file_path = f.name
        f.write("Direct script test content.\n" * 50)
    
    output_path = test_file_path + ".zmt"
    
    try:
        import subprocess
        
        # Run the script directly
        result = subprocess.run(
            ["./zmt.sh", test_file_path, output_path, "false"],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        print(f"Script exit code: {result.returncode}")
        print(f"Script stdout:\n{result.stdout}")
        if result.stderr:
            print(f"Script stderr:\n{result.stderr}")
        
        if result.returncode == 0:
            print("✅ Script completed successfully!")
            if os.path.exists(output_path):
                print(f"✅ Output file created: {output_path}")
                print(f"   Size: {os.path.getsize(output_path)} bytes")
            else:
                print("❌ Output file not created")
        else:
            print(f"❌ Script failed with exit code {result.returncode}")
            
    except Exception as e:
        print(f"❌ Error running script: {e}")
    finally:
        # Clean up
        for path in [test_file_path, output_path]:
            if os.path.exists(path):
                os.remove(path)
                print(f"Cleaned up: {path}")

if __name__ == "__main__":
    print("Samuel L. Jackson's ZMT Fix Verification")
    print("Testing if we fixed that exit code bullshit!")
    print()
    
    # Test direct script first
    test_direct_script()
    
    # Test via API
    success = test_compression_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 FIX VERIFIED! The compression is working correctly now!")
        print("The ZMT exit code issue has been resolved.")
    else:
        print("💥 Fix needs more work. Check the output above for details.")
    
    print("\nNext steps:")
    print("1. If successful: Test with your original file")
    print("2. If failed: Check server logs for more details")
    print("3. The fix handles ZMT's quirky exit behavior properly")
