"""
Decompression module for handling archive restoration
"""
import subprocess
import logging
import os
import shutil
from pathlib import Path
from typing import Dict, Any
import tempfile

from config import COMPRESSION_EXTENSION, OUTPUT_DIR, ensure_directories

logger = logging.getLogger(__name__)

class DecompressionError(Exception):
    """Custom exception for decompression errors"""
    pass

def decompress_archive(archive_path: str, output_dir: str = None) -> Dict[str, Any]:
    """
    Decompress a .zmt archive file
    
    Args:
        archive_path: Path to the archive file to decompress
        output_dir: Optional output directory (defaults to OUTPUT_DIR/restored)
        
    Returns:
        Dictionary containing decompression results
    """
    ensure_directories()
    
    archive_path_obj = Path(archive_path)
    if not archive_path_obj.exists():
        raise DecompressionError(f"Archive file does not exist: {archive_path}")
    
    if not archive_path_obj.name.endswith(COMPRESSION_EXTENSION):
        raise DecompressionError(f"Invalid archive format. Expected {COMPRESSION_EXTENSION} file")
    
    # Set up output directory
    if output_dir is None:
        # Create a restored directory with the archive name
        archive_name = archive_path_obj.stem  # Remove .zmt extension
        output_dir = OUTPUT_DIR / "restored" / archive_name
    else:
        output_dir = Path(output_dir)
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        logger.info(f"Starting decompression of {archive_path} to {output_dir}")
        
        # ZMT extracts files to their original absolute paths
        # We need a different approach: extract to original paths, then copy to output_dir

        # First, list the archive contents to see what files are inside
        list_result = subprocess.run(
            [str(Path(os.getcwd()) / "zmt"), "l", str(archive_path)],
            capture_output=True,
            text=True,
            check=True,
            timeout=60
        )

        # Extract files (ZMT will restore to original paths)
        result = subprocess.run(
            [str(Path(os.getcwd()) / "zmt"), "x", str(archive_path), "-force"],
            capture_output=True,
            text=True,
            check=True,
            timeout=300  # 5 minute timeout
        )

        # Parse the extraction output to find where files were extracted
        extracted_files = []
        for line in result.stdout.split('\n'):
            if line.strip().startswith('+ ') or line.strip().startswith('= '):
                # Extract the file path from ZMT output
                file_path = line.strip()[2:].strip()
                if os.path.exists(file_path):
                    extracted_files.append(file_path)

        if not extracted_files:
            # Fallback: try to parse from list output
            for line in list_result.stdout.split('\n'):
                line = line.strip()
                if line and not line.startswith('zmt ') and not line.startswith('/tmp/') and not line.startswith('1.') and not line.startswith('->') and not line.startswith('0.'):
                    # Look for lines with file information (date, size, permissions, path)
                    parts = line.split()
                    if len(parts) >= 4 and parts[0] == '-':
                        # Format: - date time size permissions path
                        if len(parts) >= 5:
                            potential_path = ' '.join(parts[4:])  # Join in case path has spaces
                            if potential_path.startswith('/'):
                                extracted_files.append(potential_path)

        if not extracted_files:
            raise DecompressionError("Could not determine extracted file locations from ZMT output")

        # Copy extracted files to our output directory
        copied_files = []
        for src_path in extracted_files:
            src_file = Path(src_path)
            dst_file = output_dir / src_file.name

            try:
                if src_file.is_file():
                    shutil.copy2(src_file, dst_file)
                    copied_files.append(str(dst_file))
                elif src_file.is_dir():
                    shutil.copytree(src_file, dst_file, dirs_exist_ok=True)
                    copied_files.append(str(dst_file))
            except Exception as e:
                logger.warning(f"Failed to copy {src_path} to {dst_file}: {e}")

        if not copied_files:
            raise DecompressionError("Failed to copy extracted files to output directory")

        # Verify that files were extracted
        extracted_items = list(output_dir.iterdir())
        if not extracted_items:
            raise DecompressionError("Decompression completed but no files were extracted")
        
        # Calculate total size of extracted files
        total_size = 0
        file_count = 0
        for item in output_dir.rglob("*"):
            if item.is_file():
                total_size += item.stat().st_size
                file_count += 1
        
        return {
            "status": "success",
            "restored_path": str(output_dir),
            "extracted_files": file_count,
            "total_size": format_size(total_size),
            "total_size_bytes": total_size
        }
        
    except subprocess.TimeoutExpired:
        raise DecompressionError("Decompression timed out after 5 minutes")
    except subprocess.CalledProcessError as e:
        error_msg = f"Decompression failed: {e.stderr}"
        logger.error(error_msg)
        raise DecompressionError(error_msg)
    except Exception as e:
        logger.error(f"Unexpected error during decompression: {str(e)}")
        raise DecompressionError(f"Decompression failed: {str(e)}")

def format_size(size_bytes: int) -> str:
    """Convert bytes to human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f}{unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f}PB"

def list_archive_contents(archive_path: str) -> Dict[str, Any]:
    """
    List the contents of an archive without extracting it
    
    Args:
        archive_path: Path to the archive file
        
    Returns:
        Dictionary containing archive contents information
    """
    archive_path_obj = Path(archive_path)
    if not archive_path_obj.exists():
        raise DecompressionError(f"Archive file does not exist: {archive_path}")
    
    if not archive_path_obj.name.endswith(COMPRESSION_EXTENSION):
        raise DecompressionError(f"Invalid archive format. Expected {COMPRESSION_EXTENSION} file")
    
    try:
        # For ZMT archives, we'll need to use a different approach
        # Since ZMT doesn't have a direct list command, we'll return basic info
        archive_size = archive_path_obj.stat().st_size

        # For now, return basic archive information
        # In a real implementation, you might extract to a temp dir to list contents
        result = subprocess.run(
            ["file", str(archive_path)],
            capture_output=True,
            text=True,
            check=True,
            timeout=60  # 1 minute timeout for file info
        )
        
        file_info = result.stdout.strip() if result.stdout.strip() else "ZMT archive"

        return {
            "status": "success",
            "archive_path": str(archive_path),
            "archive_size": format_size(archive_size),
            "archive_size_bytes": archive_size,
            "file_info": file_info,
            "note": "ZMT archive - extract to see contents"
        }
        
    except subprocess.TimeoutExpired:
        raise DecompressionError("Archive listing timed out")
    except subprocess.CalledProcessError as e:
        error_msg = f"Failed to list archive contents: {e.stderr}"
        logger.error(error_msg)
        raise DecompressionError(error_msg)
    except Exception as e:
        logger.error(f"Unexpected error listing archive: {str(e)}")
        raise DecompressionError(f"Archive listing failed: {str(e)}")
