#!/usr/bin/env python3
"""
Test runner for deleteOriginal functionality
"""
import unittest
import sys
import os

# Add current directory to path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def run_all_tests():
    """Run all tests for deleteOriginal functionality"""
    
    print("=" * 60)
    print("RUNNING TESTS FOR deleteOriginal FUNCTIONALITY")
    print("=" * 60)
    
    # Discover and run unit tests
    print("\n1. Running Unit Tests...")
    print("-" * 40)
    
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Load unit tests
    try:
        from test_compression_delete_original import TestDeleteOriginalFunctionality
        suite.addTests(loader.loadTestsFromTestCase(TestDeleteOriginalFunctionality))
        print("✓ Loaded unit tests for compression functions")
    except ImportError as e:
        print(f"✗ Failed to load unit tests: {e}")
    
    # Load API integration tests
    try:
        from test_api_delete_original import TestDeleteOriginalAPI, TestDeleteOriginalParameterTypes
        suite.addTests(loader.loadTestsFromTestCase(TestDeleteOriginalAPI))
        suite.addTests(loader.loadTestsFromTestCase(TestDeleteOriginalParameterTypes))
        print("✓ Loaded API integration tests")
    except ImportError as e:
        print(f"✗ Failed to load API tests: {e}")
    
    # Run the tests
    print("\n2. Executing Tests...")
    print("-" * 40)
    
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    skipped = len(result.skipped) if hasattr(result, 'skipped') else 0
    passed = total_tests - failures - errors - skipped
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed}")
    print(f"Failed: {failures}")
    print(f"Errors: {errors}")
    print(f"Skipped: {skipped}")
    
    if failures > 0:
        print("\nFAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if errors > 0:
        print("\nERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback.split('Exception:')[-1].strip()}")
    
    success = failures == 0 and errors == 0
    
    if success:
        print("\n🎉 ALL TESTS PASSED! The deleteOriginal functionality is working correctly.")
    else:
        print(f"\n❌ {failures + errors} test(s) failed. Check the output above for details.")
    
    return success

def run_quick_syntax_check():
    """Quick syntax check for all Python files"""
    print("Running syntax check...")
    
    files_to_check = [
        "compression.py",
        "main.py", 
        "test_compression_delete_original.py",
        "test_api_delete_original.py"
    ]
    
    for file in files_to_check:
        if os.path.exists(file):
            try:
                with open(file, 'r') as f:
                    compile(f.read(), file, 'exec')
                print(f"✓ {file} - syntax OK")
            except SyntaxError as e:
                print(f"✗ {file} - syntax error: {e}")
                return False
        else:
            print(f"⚠ {file} - file not found")
    
    return True

if __name__ == "__main__":
    print("Samuel L. Jackson's Compression API Test Suite")
    print("Time to test this deleteOriginal functionality!")
    
    # Quick syntax check first
    if not run_quick_syntax_check():
        print("\n❌ Syntax errors found. Fix them before running tests.")
        sys.exit(1)
    
    # Run all tests
    success = run_all_tests()
    
    if success:
        print("\n🚀 Ready to compress and delete like a boss!")
        sys.exit(0)
    else:
        print("\n💥 Tests failed. Time to debug this shit!")
        sys.exit(1)
