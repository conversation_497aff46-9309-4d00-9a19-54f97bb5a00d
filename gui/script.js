// ZMT Compression Platform GUI
class CompressionGUI {
    constructor() {
        this.apiBase = window.location.origin;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkAPIStatus();
        this.setupTabNavigation();
        this.setupDragAndDrop();
        this.setupKeyboardShortcuts();
        this.updateRecentOperationsDisplay();
    }

    // API Status Check
    async checkAPIStatus() {
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');
        
        try {
            const response = await fetch(`${this.apiBase}/api/status`);
            const data = await response.json();
            
            if (response.ok && data.status === 'ok') {
                statusDot.className = 'status-dot online';
                statusText.textContent = 'Online';
            } else {
                throw new Error('API not responding correctly');
            }
        } catch (error) {
            statusDot.className = 'status-dot offline';
            statusText.textContent = 'Offline';
            this.showMessage('API connection failed', 'error');
        }
    }

    // Tab Navigation
    setupTabNavigation() {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetTab = btn.dataset.tab;
                
                // Remove active class from all tabs and contents
                tabBtns.forEach(b => b.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding content
                btn.classList.add('active');
                document.getElementById(`${targetTab}-tab`).classList.add('active');
            });
        });
    }

    // Event Listeners
    setupEventListeners() {
        // File input triggers
        document.getElementById('browseFilesBtn').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });
        
        document.getElementById('browseArchiveBtn').addEventListener('click', () => {
            document.getElementById('archiveInput').click();
        });

        // File input changes
        document.getElementById('fileInput').addEventListener('change', (e) => {
            this.handleFileSelection(e.target.files, 'selectedFiles');
        });
        
        document.getElementById('archiveInput').addEventListener('change', (e) => {
            this.handleFileSelection(e.target.files, 'selectedArchive');
        });

        // Action buttons
        document.getElementById('compressBtn').addEventListener('click', () => {
            this.handleCompress();
        });
        
        document.getElementById('decompressBtn').addEventListener('click', () => {
            this.handleDecompress();
        });
        
        document.getElementById('listContentsBtn').addEventListener('click', () => {
            this.handleListContents();
        });

        // Path browse buttons (placeholder for now)
        document.getElementById('browsePathBtn').addEventListener('click', () => {
            this.showMessage('Path browsing not implemented yet', 'warning');
        });
        
        document.getElementById('browseArchivePathBtn').addEventListener('click', () => {
            this.showMessage('Path browsing not implemented yet', 'warning');
        });
        
        document.getElementById('browseInfoArchiveBtn').addEventListener('click', () => {
            this.showMessage('Path browsing not implemented yet', 'warning');
        });
    }

    // Drag and Drop
    setupDragAndDrop() {
        const compressDropZone = document.getElementById('compressDropZone');
        const decompressDropZone = document.getElementById('decompressDropZone');

        // Compress drop zone
        this.setupDropZone(compressDropZone, (files) => {
            document.getElementById('fileInput').files = files;
            this.handleFileSelection(files, 'selectedFiles');
        });

        // Decompress drop zone
        this.setupDropZone(decompressDropZone, (files) => {
            const zmtFiles = Array.from(files).filter(file => file.name.endsWith('.zmt'));
            if (zmtFiles.length > 0) {
                document.getElementById('archiveInput').files = this.createFileList(zmtFiles);
                this.handleFileSelection(zmtFiles, 'selectedArchive');
            } else {
                this.showMessage('Please drop .zmt archive files only', 'error');
            }
        });
    }

    setupDropZone(dropZone, onDrop) {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => {
                dropZone.classList.add('drag-over');
            });
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => {
                dropZone.classList.remove('drag-over');
            });
        });

        dropZone.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            onDrop(files);
        });
    }

    // File Selection Handler
    handleFileSelection(files, containerId) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        if (files.length === 0) return;

        try {
            // Validate files based on container type
            if (containerId === 'selectedArchive') {
                this.validateFiles(files, ['.zmt']);
            } else {
                this.validateFiles(files);
            }

            Array.from(files).forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <span class="file-name">${file.name}</span>
                    <span class="file-size">${this.formatFileSize(file.size)}</span>
                `;
                container.appendChild(fileItem);
            });
        } catch (error) {
            this.showMessage(error.message, 'error');
            // Clear the file input
            if (containerId === 'selectedFiles') {
                document.getElementById('fileInput').value = '';
            } else if (containerId === 'selectedArchive') {
                document.getElementById('archiveInput').value = '';
            }
        }
    }

    // Compression Handler
    async handleCompress() {
        const fileInput = document.getElementById('fileInput');
        const localPath = document.getElementById('localPath').value.trim();
        const maxCompress = document.getElementById('maxCompress').checked;
        const deleteOriginal = document.getElementById('deleteOriginal').checked;
        const outputFilename = document.getElementById('outputFilename').value.trim();

        if (fileInput.files.length === 0 && !localPath) {
            this.showMessage('Please select files or enter a local path', 'error');
            return;
        }

        // Validate output filename if provided
        if (outputFilename && !outputFilename.endsWith('.zmt')) {
            this.showMessage('Output filename should end with .zmt extension', 'warning');
        }

        // Clear previous results
        document.getElementById('compressResults').hidden = true;

        this.showProgress('compressProgress', 'Compressing files...');
        this.setButtonLoading('compressBtn', true);

        try {
            let response;
            
            if (fileInput.files.length > 0) {
                // File upload mode
                const formData = new FormData();
                Array.from(fileInput.files).forEach(file => {
                    formData.append('files', file);
                });
                if (outputFilename) formData.append('output_filename', outputFilename);
                if (maxCompress) formData.append('maxCompress', 'true');
                if (deleteOriginal) formData.append('deleteOriginal', 'true');

                response = await fetch(`${this.apiBase}/api/compress`, {
                    method: 'POST',
                    body: formData
                });
            } else {
                // Local path mode
                const formData = new URLSearchParams();
                formData.append('input_path', localPath);
                if (outputFilename) formData.append('output_filename', outputFilename);
                if (maxCompress) formData.append('maxCompress', 'true');
                if (deleteOriginal) formData.append('deleteOriginal', 'true');

                response = await fetch(`${this.apiBase}/api/compress`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: formData
                });
            }

            const result = await response.json();
            
            if (response.ok) {
                this.showCompressionResults(result);
                this.showMessage('Compression completed successfully!', 'success');
                this.clearCompressionForm();
            } else {
                throw new Error(result.detail || 'Compression failed');
            }
        } catch (error) {
            this.showMessage(`Compression error: ${error.message}`, 'error');
        } finally {
            this.hideProgress('compressProgress');
            this.setButtonLoading('compressBtn', false);
        }
    }

    // Decompression Handler
    async handleDecompress() {
        const archiveInput = document.getElementById('archiveInput');
        const archivePath = document.getElementById('archivePath').value.trim();
        const outputDir = document.getElementById('outputDir').value.trim();

        if (archiveInput.files.length === 0 && !archivePath) {
            this.showMessage('Please select an archive or enter an archive path', 'error');
            return;
        }

        // Validate archive path if provided
        if (archivePath && !archivePath.toLowerCase().endsWith('.zmt')) {
            this.showMessage('Archive path should point to a .zmt file', 'error');
            return;
        }

        // Clear previous results
        document.getElementById('decompressResults').hidden = true;

        this.showProgress('decompressProgress', 'Decompressing archive...');
        this.setButtonLoading('decompressBtn', true);

        try {
            let response;
            
            if (archiveInput.files.length > 0) {
                // File upload mode
                const formData = new FormData();
                formData.append('archive_file', archiveInput.files[0]);
                if (outputDir) formData.append('output_dir', outputDir);

                response = await fetch(`${this.apiBase}/api/uncompress`, {
                    method: 'POST',
                    body: formData
                });
            } else {
                // Local path mode
                const formData = new URLSearchParams();
                formData.append('archive_path', archivePath);
                if (outputDir) formData.append('output_dir', outputDir);

                response = await fetch(`${this.apiBase}/api/uncompress`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: formData
                });
            }

            const result = await response.json();
            
            if (response.ok) {
                this.showDecompressionResults(result);
                this.showMessage('Decompression completed successfully!', 'success');
                this.clearDecompressionForm();
            } else {
                throw new Error(result.detail || 'Decompression failed');
            }
        } catch (error) {
            let errorMessage = error.message;

            // Provide helpful error messages for common issues
            if (errorMessage.includes('password incorrect')) {
                errorMessage = 'Archive appears to be password protected. Please check the ZMT configuration or try a different archive.';
            } else if (errorMessage.includes('not found')) {
                errorMessage = 'Archive file not found. Please check the file path.';
            }

            this.showMessage(`Decompression error: ${errorMessage}`, 'error');
        } finally {
            this.hideProgress('decompressProgress');
            this.setButtonLoading('decompressBtn', false);
        }
    }

    // Archive Contents Handler
    async handleListContents() {
        const archivePath = document.getElementById('infoArchivePath').value.trim();

        if (!archivePath) {
            this.showMessage('Please enter an archive path', 'error');
            return;
        }

        // Validate .zmt extension
        if (!archivePath.toLowerCase().endsWith('.zmt')) {
            this.showMessage('Please enter a valid .zmt archive path', 'error');
            return;
        }

        this.showProgress('listProgress', 'Reading archive...');
        this.setButtonLoading('listContentsBtn', true);

        try {
            const response = await fetch(`${this.apiBase}/api/archive/contents?archive_path=${encodeURIComponent(archivePath)}`);
            const result = await response.json();

            if (response.ok) {
                this.showArchiveContents(result);
                this.showMessage('Archive contents loaded successfully!', 'success');
            } else {
                throw new Error(result.detail || 'Failed to read archive');
            }
        } catch (error) {
            this.showMessage(`Archive reading error: ${error.message}`, 'error');
        } finally {
            this.hideProgress('listProgress');
            this.setButtonLoading('listContentsBtn', false);
        }
    }

    // Results Display Methods
    showCompressionResults(result) {
        const container = document.getElementById('compressResultsContent');

        // Calculate compression percentage for visualization
        const compressionPercent = parseFloat(result.compression_ratio.replace('%', ''));
        const savingsPercent = 100 - compressionPercent;

        container.innerHTML = `
            <div class="result-item">
                <div class="result-label">Output Path</div>
                <div class="result-value">${result.output_path}</div>
            </div>
            <div class="result-item">
                <div class="result-label">Original Size</div>
                <div class="result-value">${result.original_size}</div>
            </div>
            <div class="result-item">
                <div class="result-label">Compressed Size</div>
                <div class="result-value">${result.compressed_size}</div>
            </div>
            <div class="result-item">
                <div class="result-label">Compression Ratio</div>
                <div class="result-value">${result.compression_ratio}</div>
            </div>
            <div class="result-item compression-visual">
                <div class="result-label">Space Saved</div>
                <div class="compression-bar">
                    <div class="compression-fill" style="width: ${savingsPercent}%"></div>
                    <span class="compression-text">${savingsPercent.toFixed(1)}% saved</span>
                </div>
            </div>
        `;

        // Add copy button for output path
        this.addCopyButton(container, result.output_path);

        document.getElementById('compressResults').hidden = false;

        // Store in recent operations
        this.addToRecentOperations('compress', {
            type: 'Compression',
            output: result.output_path,
            ratio: result.compression_ratio,
            timestamp: new Date().toLocaleString()
        });
    }

    showDecompressionResults(result) {
        const container = document.getElementById('decompressResultsContent');
        container.innerHTML = `
            <div class="result-item">
                <div class="result-label">Restored Path</div>
                <div class="result-value">${result.restored_path}</div>
            </div>
            <div class="result-item">
                <div class="result-label">Extracted Files</div>
                <div class="result-value">${result.extracted_files} files</div>
            </div>
            <div class="result-item">
                <div class="result-label">Total Size</div>
                <div class="result-value">${result.total_size}</div>
            </div>
        `;

        // Add copy button for restored path
        this.addCopyButton(container, result.restored_path);

        document.getElementById('decompressResults').hidden = false;

        // Store in recent operations
        this.addToRecentOperations('decompress', {
            type: 'Decompression',
            output: result.restored_path,
            files: result.extracted_files,
            timestamp: new Date().toLocaleString()
        });
    }

    showArchiveContents(result) {
        const container = document.getElementById('archiveContentsTree');
        container.innerHTML = '';

        // Display archive information in a structured format
        const archiveInfo = document.createElement('div');
        archiveInfo.className = 'archive-info-grid';
        archiveInfo.innerHTML = `
            <div class="result-item">
                <div class="result-label">Archive Path</div>
                <div class="result-value">${result.archive_path}</div>
            </div>
            <div class="result-item">
                <div class="result-label">Archive Size</div>
                <div class="result-value">${result.archive_size}</div>
            </div>
            <div class="result-item">
                <div class="result-label">File Type</div>
                <div class="result-value">${result.file_info}</div>
            </div>
            <div class="result-item">
                <div class="result-label">Note</div>
                <div class="result-value">${result.note}</div>
            </div>
        `;

        container.appendChild(archiveInfo);

        // Add a note about ZMT limitations
        const noteDiv = document.createElement('div');
        noteDiv.className = 'archive-note';
        noteDiv.innerHTML = `
            <p><strong>ℹ️ ZMT Archive Information</strong></p>
            <p>ZMT archives don't support listing contents without extraction. To see the files inside, use the Decompress tab to extract the archive.</p>
        `;
        container.appendChild(noteDiv);

        document.getElementById('archiveResults').hidden = false;
    }

    // Utility Methods
    showProgress(progressId, message) {
        const progressElement = document.getElementById(progressId);
        const textElement = progressElement.querySelector('.progress-text');
        textElement.textContent = message;
        progressElement.hidden = false;
    }

    hideProgress(progressId) {
        document.getElementById(progressId).hidden = true;
    }

    setButtonLoading(buttonId, loading) {
        const button = document.getElementById(buttonId);
        const textSpan = button.querySelector('.btn-text');
        const spinnerSpan = button.querySelector('.btn-spinner');
        
        button.disabled = loading;
        textSpan.hidden = loading;
        spinnerSpan.hidden = !loading;
    }

    showMessage(message, type = 'info') {
        const container = document.getElementById('statusMessages');
        container.innerHTML = `<span class="text-${type}">${message}</span>`;
        
        // Auto-clear after 5 seconds
        setTimeout(() => {
            container.innerHTML = '';
        }, 5000);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    createFileList(files) {
        const dt = new DataTransfer();
        files.forEach(file => dt.items.add(file));
        return dt.files;
    }

    // Form clearing methods
    clearCompressionForm() {
        document.getElementById('fileInput').value = '';
        document.getElementById('localPath').value = '';
        document.getElementById('outputFilename').value = '';
        document.getElementById('maxCompress').checked = false;
        document.getElementById('deleteOriginal').checked = false;
        document.getElementById('selectedFiles').innerHTML = '';
    }

    clearDecompressionForm() {
        document.getElementById('archiveInput').value = '';
        document.getElementById('archivePath').value = '';
        document.getElementById('outputDir').value = '';
        document.getElementById('selectedArchive').innerHTML = '';
    }

    // Enhanced file validation
    validateFiles(files, allowedExtensions = null) {
        const maxSize = 10 * 1024 * 1024 * 1024; // 10GB as per config
        let totalSize = 0;

        for (let file of files) {
            totalSize += file.size;

            if (file.size > maxSize) {
                throw new Error(`File "${file.name}" exceeds maximum size limit of 10GB`);
            }

            if (allowedExtensions && !allowedExtensions.some(ext => file.name.toLowerCase().endsWith(ext))) {
                throw new Error(`File "${file.name}" has invalid extension. Allowed: ${allowedExtensions.join(', ')}`);
            }
        }

        const maxTotalSize = 50 * 1024 * 1024 * 1024; // 50GB as per config
        if (totalSize > maxTotalSize) {
            throw new Error(`Total file size exceeds maximum limit of 50GB`);
        }

        return true;
    }

    // Copy to clipboard functionality
    addCopyButton(container, textToCopy) {
        const copyBtn = document.createElement('button');
        copyBtn.className = 'btn btn-secondary copy-btn';
        copyBtn.innerHTML = '📋 Copy Path';
        copyBtn.onclick = () => this.copyToClipboard(textToCopy);

        const copyContainer = document.createElement('div');
        copyContainer.className = 'result-item copy-container';
        copyContainer.appendChild(copyBtn);
        container.appendChild(copyContainer);
    }

    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showMessage('Path copied to clipboard!', 'success');
        } catch (err) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showMessage('Path copied to clipboard!', 'success');
        }
    }

    // Recent operations management
    addToRecentOperations(type, operation) {
        const key = 'zmt_recent_operations';
        let recent = JSON.parse(localStorage.getItem(key) || '[]');

        // Add new operation to the beginning
        recent.unshift(operation);

        // Keep only last 10 operations
        recent = recent.slice(0, 10);

        localStorage.setItem(key, JSON.stringify(recent));
        this.updateRecentOperationsDisplay();
    }

    updateRecentOperationsDisplay() {
        // This could be implemented to show recent operations in a sidebar or dropdown
        // For now, we'll just log it
        const recent = JSON.parse(localStorage.getItem('zmt_recent_operations') || '[]');
        console.log('Recent operations:', recent);
    }

    // Keyboard shortcuts
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Only trigger if not typing in an input field
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;

            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case '1':
                        e.preventDefault();
                        this.switchToTab('compress');
                        break;
                    case '2':
                        e.preventDefault();
                        this.switchToTab('decompress');
                        break;
                    case '3':
                        e.preventDefault();
                        this.switchToTab('archive');
                        break;
                    case 'Enter':
                        e.preventDefault();
                        this.executeCurrentTabAction();
                        break;
                }
            }

            // ESC to clear forms
            if (e.key === 'Escape') {
                this.clearAllForms();
            }
        });
    }

    switchToTab(tabName) {
        const tabBtn = document.querySelector(`[data-tab="${tabName}"]`);
        if (tabBtn) {
            tabBtn.click();
        }
    }

    executeCurrentTabAction() {
        const activeTab = document.querySelector('.tab-content.active');
        if (activeTab) {
            const tabId = activeTab.id;
            if (tabId === 'compress-tab') {
                this.handleCompress();
            } else if (tabId === 'decompress-tab') {
                this.handleDecompress();
            } else if (tabId === 'archive-tab') {
                this.handleListContents();
            }
        }
    }

    clearAllForms() {
        this.clearCompressionForm();
        this.clearDecompressionForm();
        document.getElementById('infoArchivePath').value = '';
        this.showMessage('Forms cleared', 'info');
    }
}

// Initialize the GUI when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new CompressionGUI();
});
