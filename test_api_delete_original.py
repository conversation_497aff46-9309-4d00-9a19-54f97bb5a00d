#!/usr/bin/env python3
"""
Integration tests for the deleteOriginal API functionality
"""
import unittest
import tempfile
import os
import shutil
from pathlib import Path
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
import io

# Import the FastAPI app
from main import app


class TestDeleteOriginalAPI(unittest.TestCase):
    """Integration tests for deleteOriginal parameter in API endpoints"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.client = TestClient(app)
        self.test_dir = Path(tempfile.mkdtemp())
        self.test_file = self.test_dir / "test_file.txt"
        self.test_file.write_text("This is a test file for API testing\n" * 100)
    
    def tearDown(self):
        """Clean up test fixtures"""
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    @patch('main.compress_file')
    def test_api_compress_delete_original_true(self, mock_compress_file):
        """Test API endpoint with deleteOriginal=true"""
        # Mock successful compression with deletion
        mock_compress_file.return_value = {
            "status": "success",
            "output_path": "/tmp/test_file.txt.zmt",
            "original_size": "10.0KB",
            "compressed_size": "5.0KB",
            "compression_ratio": "50.0%",
            "original_deleted": True
        }
        
        # Make API request with deleteOriginal=true
        response = self.client.post(
            "/api/compress",
            data={
                "input_path": str(self.test_file),
                "deleteOriginal": "true"
            }
        )
        
        # Verify response
        self.assertEqual(response.status_code, 200)
        result = response.json()
        self.assertEqual(result["status"], "success")
        self.assertTrue(result.get("original_deleted", False))
        
        # Verify compress_file was called with correct parameters
        mock_compress_file.assert_called_once_with(
            str(self.test_file), None, False, True
        )
    
    @patch('main.compress_file')
    def test_api_compress_delete_original_false(self, mock_compress_file):
        """Test API endpoint with deleteOriginal=false"""
        # Mock successful compression without deletion
        mock_compress_file.return_value = {
            "status": "success",
            "output_path": "/tmp/test_file.txt.zmt",
            "original_size": "10.0KB",
            "compressed_size": "5.0KB",
            "compression_ratio": "50.0%"
        }
        
        # Make API request with deleteOriginal=false
        response = self.client.post(
            "/api/compress",
            data={
                "input_path": str(self.test_file),
                "deleteOriginal": "false"
            }
        )
        
        # Verify response
        self.assertEqual(response.status_code, 200)
        result = response.json()
        self.assertEqual(result["status"], "success")
        self.assertNotIn("original_deleted", result)
        
        # Verify compress_file was called with correct parameters
        mock_compress_file.assert_called_once_with(
            str(self.test_file), None, False, False
        )
    
    @patch('main.compress_file')
    def test_api_compress_delete_original_default(self, mock_compress_file):
        """Test API endpoint with deleteOriginal not specified (should default to false)"""
        # Mock successful compression without deletion
        mock_compress_file.return_value = {
            "status": "success",
            "output_path": "/tmp/test_file.txt.zmt",
            "original_size": "10.0KB",
            "compressed_size": "5.0KB",
            "compression_ratio": "50.0%"
        }
        
        # Make API request without deleteOriginal parameter
        response = self.client.post(
            "/api/compress",
            data={
                "input_path": str(self.test_file)
            }
        )
        
        # Verify response
        self.assertEqual(response.status_code, 200)
        result = response.json()
        self.assertEqual(result["status"], "success")
        
        # Verify compress_file was called with deleteOriginal=False (default)
        mock_compress_file.assert_called_once_with(
            str(self.test_file), None, False, False
        )
    
    @patch('main.compress_file')
    def test_api_compress_with_all_parameters(self, mock_compress_file):
        """Test API endpoint with all parameters including deleteOriginal"""
        # Mock successful compression with deletion
        mock_compress_file.return_value = {
            "status": "success",
            "output_path": "/tmp/custom_output.zmt",
            "original_size": "10.0KB",
            "compressed_size": "4.0KB",
            "compression_ratio": "60.0%",
            "original_deleted": True
        }
        
        # Make API request with all parameters
        response = self.client.post(
            "/api/compress",
            data={
                "input_path": str(self.test_file),
                "output_filename": "custom_output.zmt",
                "maxCompress": "true",
                "deleteOriginal": "true"
            }
        )
        
        # Verify response
        self.assertEqual(response.status_code, 200)
        result = response.json()
        self.assertEqual(result["status"], "success")
        self.assertTrue(result.get("original_deleted", False))
        
        # Verify compress_file was called with correct parameters
        mock_compress_file.assert_called_once_with(
            str(self.test_file), "custom_output.zmt", True, True
        )
    
    @patch('main.compress_file')
    def test_api_compress_deletion_error(self, mock_compress_file):
        """Test API endpoint when compression succeeds but deletion fails"""
        # Mock compression success with deletion error
        mock_compress_file.return_value = {
            "status": "success",
            "output_path": "/tmp/test_file.txt.zmt",
            "original_size": "10.0KB",
            "compressed_size": "5.0KB",
            "compression_ratio": "50.0%",
            "original_deleted": False,
            "deletion_error": "Permission denied"
        }
        
        # Make API request with deleteOriginal=true
        response = self.client.post(
            "/api/compress",
            data={
                "input_path": str(self.test_file),
                "deleteOriginal": "true"
            }
        )
        
        # Verify response
        self.assertEqual(response.status_code, 200)
        result = response.json()
        self.assertEqual(result["status"], "success")
        self.assertFalse(result.get("original_deleted", True))
        self.assertEqual(result.get("deletion_error"), "Permission denied")
    
    def test_api_compress_uploaded_files_ignore_delete_original(self):
        """Test that deleteOriginal is ignored for uploaded files"""
        # Create test file content
        file_content = b"This is test file content for upload testing"
        
        # Make API request with file upload and deleteOriginal=true
        response = self.client.post(
            "/api/compress",
            files={"files": ("test_upload.txt", io.BytesIO(file_content), "text/plain")},
            data={"deleteOriginal": "true"}
        )
        
        # For uploaded files, the test should pass regardless of deleteOriginal
        # because uploaded files are in temp directories that get cleaned up automatically
        # The actual behavior depends on the compression script working, so we'll just
        # verify the request is processed without error
        self.assertIn(response.status_code, [200, 500])  # 500 if compression script fails
    
    def test_api_compress_invalid_path_with_delete_original(self):
        """Test API endpoint with invalid path and deleteOriginal=true"""
        # Make API request with non-existent file
        response = self.client.post(
            "/api/compress",
            data={
                "input_path": "/non/existent/file.txt",
                "deleteOriginal": "true"
            }
        )
        
        # Verify error response
        self.assertEqual(response.status_code, 404)
        result = response.json()
        self.assertIn("not found", result["detail"].lower())
    
    def test_api_compress_no_input_with_delete_original(self):
        """Test API endpoint with no input but deleteOriginal specified"""
        # Make API request without input_path or files
        response = self.client.post(
            "/api/compress",
            data={"deleteOriginal": "true"}
        )
        
        # Verify error response
        self.assertEqual(response.status_code, 400)
        result = response.json()
        self.assertIn("upload files or provide input_path", result["detail"])


class TestDeleteOriginalParameterTypes(unittest.TestCase):
    """Test different parameter type variations for deleteOriginal"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.client = TestClient(app)
        self.test_dir = Path(tempfile.mkdtemp())
        self.test_file = self.test_dir / "test_file.txt"
        self.test_file.write_text("Test content")
    
    def tearDown(self):
        """Clean up test fixtures"""
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    @patch('main.compress_file')
    def test_delete_original_string_true(self, mock_compress_file):
        """Test deleteOriginal with string 'true'"""
        mock_compress_file.return_value = {"status": "success"}
        
        response = self.client.post(
            "/api/compress",
            data={
                "input_path": str(self.test_file),
                "deleteOriginal": "true"
            }
        )
        
        self.assertEqual(response.status_code, 200)
        mock_compress_file.assert_called_once_with(
            str(self.test_file), None, False, True
        )
    
    @patch('main.compress_file')
    def test_delete_original_string_false(self, mock_compress_file):
        """Test deleteOriginal with string 'false'"""
        mock_compress_file.return_value = {"status": "success"}
        
        response = self.client.post(
            "/api/compress",
            data={
                "input_path": str(self.test_file),
                "deleteOriginal": "false"
            }
        )
        
        self.assertEqual(response.status_code, 200)
        mock_compress_file.assert_called_once_with(
            str(self.test_file), None, False, False
        )


if __name__ == "__main__":
    unittest.main(verbosity=2)
