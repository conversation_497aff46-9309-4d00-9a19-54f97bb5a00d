"""
Configuration settings for the Compression API
"""
import os
from pathlib import Path

# API Settings
API_HOST = "0.0.0.0"
API_PORT = 8000
API_TITLE = "Lossless Compression API"
API_VERSION = "1.0.0"
API_DESCRIPTION = "Phase 1 - Local compression/decompression API using FastAPI"

# File paths
BASE_DIR = Path(__file__).parent
TEMP_DIR = Path("/tmp/compression_api")
UPLOAD_DIR = TEMP_DIR / "uploads"
OUTPUT_DIR = TEMP_DIR / "outputs"

# Compression script settings
COMPRESSION_SCRIPT = BASE_DIR / "zmt.sh"
COMPRESSION_EXTENSION = ".zmt"

# File size limits (in bytes)
MAX_FILE_SIZE = 10 * 1024 * 1024 * 1024  # 10GB
MAX_TOTAL_SIZE = 50 * 1024 * 1024 * 1024  # 50GB

# Supported file types (empty means all types allowed)
ALLOWED_EXTENSIONS = set()  # Allow all file types for now

# Create necessary directories
def ensure_directories():
    """Create necessary directories if they don't exist"""
    TEMP_DIR.mkdir(exist_ok=True)
    UPLOAD_DIR.mkdir(exist_ok=True)
    OUTPUT_DIR.mkdir(exist_ok=True)

# Logging configuration
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        },
    },
    "handlers": {
        "default": {
            "formatter": "default",
            "class": "logging.StreamHandler",
            "stream": "ext://sys.stdout",
        },
    },
    "root": {
        "level": "INFO",
        "handlers": ["default"],
    },
}
