{"id": "zmt-compression-env", "name": "ZMT Compression API Environment", "values": [{"key": "base_url", "value": "http://localhost:8000", "description": "Base URL for the ZMT Compression API", "type": "default", "enabled": true}, {"key": "last_compressed_file", "value": "", "description": "Path to the last compressed file (auto-populated by tests)", "type": "default", "enabled": true}, {"key": "sample_file_path", "value": "/Users/<USER>/Documents/sample.txt", "description": "Sample file path for testing local compression", "type": "default", "enabled": true}, {"key": "sample_directory_path", "value": "/Users/<USER>/Documents/sample_folder", "description": "Sample directory path for testing directory compression", "type": "default", "enabled": true}, {"key": "custom_output_dir", "value": "/tmp/my_extractions", "description": "Custom output directory for decompression", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-08-04T18:15:00.000Z", "_postman_exported_using": "Postman/10.0.0"}