#!/usr/bin/env python3
"""
Unit tests for the deleteOriginal functionality in compression API
"""
import unittest
import tempfile
import os
import shutil
from pathlib import Path
from unittest.mock import patch, MagicMock

# Import the functions we're testing
from compression import compress_file, compress_multiple_files, CompressionError


class TestDeleteOriginalFunctionality(unittest.TestCase):
    """Test cases for deleteOriginal parameter in compression functions"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.test_file = self.test_dir / "test_file.txt"
        self.test_file.write_text("This is a test file for compression testing\n" * 100)
        
        # Create a test directory with files
        self.test_subdir = self.test_dir / "test_subdir"
        self.test_subdir.mkdir()
        (self.test_subdir / "file1.txt").write_text("File 1 content")
        (self.test_subdir / "file2.txt").write_text("File 2 content")
    
    def tearDown(self):
        """Clean up test fixtures"""
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    @patch('compression.subprocess.run')
    @patch('compression.OUTPUT_DIR', new_callable=lambda: Path(tempfile.mkdtemp()))
    def test_compress_file_delete_original_true_file(self, mock_output_dir, mock_subprocess):
        """Test compress_file with delete_original=True for a single file"""
        # Mock successful compression
        mock_result = MagicMock()
        mock_result.stdout = "ORIGINAL_SIZE:1000\nCOMPRESSED_SIZE:500\nCOMPRESSION_RATIO:50%\nOUTPUT_PATH:/tmp/test.zmt"
        mock_subprocess.return_value = mock_result
        
        # Create mock output file
        output_file = mock_output_dir / "test_file.txt.zmt"
        output_file.write_text("compressed data")
        
        # Verify file exists before compression
        self.assertTrue(self.test_file.exists())
        
        # Compress with delete_original=True
        result = compress_file(str(self.test_file), delete_original=True)
        
        # Verify compression result
        self.assertEqual(result["status"], "success")
        self.assertTrue(result.get("original_deleted", False))
        
        # Verify original file was deleted
        self.assertFalse(self.test_file.exists())
    
    @patch('compression.subprocess.run')
    @patch('compression.OUTPUT_DIR', new_callable=lambda: Path(tempfile.mkdtemp()))
    def test_compress_file_delete_original_true_directory(self, mock_output_dir, mock_subprocess):
        """Test compress_file with delete_original=True for a directory"""
        # Mock successful compression
        mock_result = MagicMock()
        mock_result.stdout = "ORIGINAL_SIZE:2000\nCOMPRESSED_SIZE:1000\nCOMPRESSION_RATIO:50%\nOUTPUT_PATH:/tmp/test.zmt"
        mock_subprocess.return_value = mock_result
        
        # Create mock output file
        output_file = mock_output_dir / "test_subdir.zmt"
        output_file.write_text("compressed data")
        
        # Verify directory exists before compression
        self.assertTrue(self.test_subdir.exists())
        
        # Compress with delete_original=True
        result = compress_file(str(self.test_subdir), delete_original=True)
        
        # Verify compression result
        self.assertEqual(result["status"], "success")
        self.assertTrue(result.get("original_deleted", False))
        
        # Verify original directory was deleted
        self.assertFalse(self.test_subdir.exists())
    
    @patch('compression.subprocess.run')
    @patch('compression.OUTPUT_DIR', new_callable=lambda: Path(tempfile.mkdtemp()))
    def test_compress_file_delete_original_false(self, mock_output_dir, mock_subprocess):
        """Test compress_file with delete_original=False (default behavior)"""
        # Mock successful compression
        mock_result = MagicMock()
        mock_result.stdout = "ORIGINAL_SIZE:1000\nCOMPRESSED_SIZE:500\nCOMPRESSION_RATIO:50%\nOUTPUT_PATH:/tmp/test.zmt"
        mock_subprocess.return_value = mock_result
        
        # Create mock output file
        output_file = mock_output_dir / "test_file.txt.zmt"
        output_file.write_text("compressed data")
        
        # Verify file exists before compression
        self.assertTrue(self.test_file.exists())
        
        # Compress with delete_original=False
        result = compress_file(str(self.test_file), delete_original=False)
        
        # Verify compression result
        self.assertEqual(result["status"], "success")
        self.assertNotIn("original_deleted", result)
        
        # Verify original file still exists
        self.assertTrue(self.test_file.exists())
    
    @patch('compression.os.remove')
    @patch('compression.subprocess.run')
    @patch('compression.OUTPUT_DIR', new_callable=lambda: Path(tempfile.mkdtemp()))
    def test_compress_file_deletion_fails(self, mock_output_dir, mock_subprocess, mock_remove):
        """Test compress_file when deletion fails"""
        # Mock successful compression
        mock_result = MagicMock()
        mock_result.stdout = "ORIGINAL_SIZE:1000\nCOMPRESSED_SIZE:500\nCOMPRESSION_RATIO:50%\nOUTPUT_PATH:/tmp/test.zmt"
        mock_subprocess.return_value = mock_result
        
        # Mock deletion failure
        mock_remove.side_effect = OSError("Permission denied")
        
        # Create mock output file
        output_file = mock_output_dir / "test_file.txt.zmt"
        output_file.write_text("compressed data")
        
        # Compress with delete_original=True
        result = compress_file(str(self.test_file), delete_original=True)
        
        # Verify compression still succeeds but deletion failed
        self.assertEqual(result["status"], "success")
        self.assertFalse(result.get("original_deleted", True))
        self.assertIn("deletion_error", result)
        self.assertEqual(result["deletion_error"], "Permission denied")
    
    @patch('compression.compress_file')
    def test_compress_multiple_files_delete_original_true(self, mock_compress_file):
        """Test compress_multiple_files with delete_original=True"""
        # Create additional test files
        file2 = self.test_dir / "test_file2.txt"
        file2.write_text("Second test file content")
        
        file_paths = [str(self.test_file), str(file2)]
        
        # Mock successful compression
        mock_compress_file.return_value = {
            "status": "success",
            "output_path": "/tmp/batch_compression.zmt",
            "original_size": "2.0KB",
            "compressed_size": "1.0KB"
        }
        
        # Verify files exist before compression
        self.assertTrue(self.test_file.exists())
        self.assertTrue(file2.exists())
        
        # Compress with delete_original=True
        result = compress_multiple_files(file_paths, delete_original=True)
        
        # Verify compression result
        self.assertEqual(result["status"], "success")
        self.assertTrue(result.get("original_deleted", False))
        self.assertEqual(len(result.get("deleted_files", [])), 2)
        
        # Verify original files were deleted
        self.assertFalse(self.test_file.exists())
        self.assertFalse(file2.exists())
    
    @patch('compression.compress_file')
    def test_compress_multiple_files_delete_original_false(self, mock_compress_file):
        """Test compress_multiple_files with delete_original=False"""
        # Create additional test files
        file2 = self.test_dir / "test_file2.txt"
        file2.write_text("Second test file content")
        
        file_paths = [str(self.test_file), str(file2)]
        
        # Mock successful compression
        mock_compress_file.return_value = {
            "status": "success",
            "output_path": "/tmp/batch_compression.zmt",
            "original_size": "2.0KB",
            "compressed_size": "1.0KB"
        }
        
        # Verify files exist before compression
        self.assertTrue(self.test_file.exists())
        self.assertTrue(file2.exists())
        
        # Compress with delete_original=False
        result = compress_multiple_files(file_paths, delete_original=False)
        
        # Verify compression result
        self.assertEqual(result["status"], "success")
        self.assertNotIn("original_deleted", result)
        
        # Verify original files still exist
        self.assertTrue(self.test_file.exists())
        self.assertTrue(file2.exists())
    
    @patch('compression.compress_file')
    @patch('compression.os.remove')
    def test_compress_multiple_files_partial_deletion_failure(self, mock_remove, mock_compress_file):
        """Test compress_multiple_files when some deletions fail"""
        # Create additional test files
        file2 = self.test_dir / "test_file2.txt"
        file2.write_text("Second test file content")
        
        file_paths = [str(self.test_file), str(file2)]
        
        # Mock successful compression
        mock_compress_file.return_value = {
            "status": "success",
            "output_path": "/tmp/batch_compression.zmt",
            "original_size": "2.0KB",
            "compressed_size": "1.0KB"
        }
        
        # Mock deletion failure for second file only
        def side_effect(path):
            if "test_file2.txt" in str(path):
                raise OSError("Permission denied")

        mock_remove.side_effect = side_effect
        
        # Compress with delete_original=True
        result = compress_multiple_files(file_paths, delete_original=True)
        
        # Verify compression result
        self.assertEqual(result["status"], "success")
        self.assertTrue(result.get("original_deleted", False))
        self.assertEqual(len(result.get("deleted_files", [])), 1)
        self.assertEqual(len(result.get("deletion_errors", [])), 1)


if __name__ == "__main__":
    unittest.main(verbosity=2)
