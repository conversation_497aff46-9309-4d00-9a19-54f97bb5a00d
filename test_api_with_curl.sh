#!/bin/bash

# ZMT Compression API Test Script
# This script tests all API endpoints using curl commands

set -e

API_BASE="http://localhost:8000"
TEST_FILE="test_sample.txt"
TEST_DIR="test_sample_dir"

echo "🚀 ZMT Compression API Test Suite"
echo "=================================="

# Create test files
echo "📁 Creating test files..."
echo "This is a test file for ZMT compression API testing. It contains some sample content to demonstrate the compression capabilities." > "$TEST_FILE"

mkdir -p "$TEST_DIR"
echo "File 1 content" > "$TEST_DIR/file1.txt"
echo "File 2 content" > "$TEST_DIR/file2.txt"
echo "File 3 content" > "$TEST_DIR/file3.txt"

echo "✅ Test files created"

# Test 1: Health Check
echo ""
echo "🏥 Test 1: Health Check"
echo "----------------------"
HEALTH_RESPONSE=$(curl -s -X GET "$API_BASE/api/status")
echo "Response: $HEALTH_RESPONSE"

if echo "$HEALTH_RESPONSE" | grep -q '"status":"ok"'; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed"
    exit 1
fi

# Test 2: Compress File Upload
echo ""
echo "🗜️ Test 2: Compress File Upload"
echo "------------------------------"
COMPRESS_RESPONSE=$(curl -s -X POST "$API_BASE/api/compress" -F "files=@$TEST_FILE")
echo "Response: $COMPRESS_RESPONSE"

if echo "$COMPRESS_RESPONSE" | grep -q '"status":"success"'; then
    echo "✅ File upload compression passed"
    # Extract output path for decompression test
    OUTPUT_PATH=$(echo "$COMPRESS_RESPONSE" | grep -o '"/tmp/compression_api/outputs/[^"]*"' | tr -d '"')
    echo "📦 Archive created at: $OUTPUT_PATH"
else
    echo "❌ File upload compression failed"
    exit 1
fi

# Test 3: Compress Local Path (File)
echo ""
echo "🗜️ Test 3: Compress Local Path (File)"
echo "------------------------------------"
FULL_PATH="$(pwd)/$TEST_FILE"
COMPRESS_LOCAL_RESPONSE=$(curl -s -X POST "$API_BASE/api/compress" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "input_path=$FULL_PATH")
echo "Response: $COMPRESS_LOCAL_RESPONSE"

if echo "$COMPRESS_LOCAL_RESPONSE" | grep -q '"status":"success"'; then
    echo "✅ Local file compression passed"
else
    echo "❌ Local file compression failed"
    exit 1
fi

# Test 4: Compress Local Path (Directory)
echo ""
echo "🗜️ Test 4: Compress Local Path (Directory)"
echo "-----------------------------------------"
FULL_DIR_PATH="$(pwd)/$TEST_DIR"
COMPRESS_DIR_RESPONSE=$(curl -s -X POST "$API_BASE/api/compress" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "input_path=$FULL_DIR_PATH")
echo "Response: $COMPRESS_DIR_RESPONSE"

if echo "$COMPRESS_DIR_RESPONSE" | grep -q '"status":"success"'; then
    echo "✅ Directory compression passed"
    DIR_OUTPUT_PATH=$(echo "$COMPRESS_DIR_RESPONSE" | grep -o '"/tmp/compression_api/outputs/[^"]*"' | tr -d '"')
    echo "📦 Directory archive created at: $DIR_OUTPUT_PATH"
else
    echo "❌ Directory compression failed"
    exit 1
fi

# Test 5: Maximum Compression
echo ""
echo "🗜️ Test 5: Maximum Compression (-m5 flag)"
echo "----------------------------------------"
MAX_COMPRESS_RESPONSE=$(curl -s -X POST "$API_BASE/api/compress" \
    -F "files=@$TEST_FILE" \
    -F "maxCompress=true")
echo "Response: $MAX_COMPRESS_RESPONSE"

if echo "$MAX_COMPRESS_RESPONSE" | grep -q '"status":"success"'; then
    echo "✅ Maximum compression passed"
    MAX_OUTPUT_PATH=$(echo "$MAX_COMPRESS_RESPONSE" | grep -o '"/tmp/compression_api/outputs/[^"]*"' | tr -d '"')
    echo "📦 Max compressed archive created at: $MAX_OUTPUT_PATH"
else
    echo "❌ Maximum compression failed"
    exit 1
fi

# Test 6: List Archive Contents
echo ""
echo "📋 Test 6: List Archive Contents"
echo "-------------------------------"
if [ -n "$OUTPUT_PATH" ]; then
    LIST_RESPONSE=$(curl -s -X GET "$API_BASE/api/archive/contents?archive_path=$OUTPUT_PATH")
    echo "Response: $LIST_RESPONSE"
    
    if echo "$LIST_RESPONSE" | grep -q '"status":"success"'; then
        echo "✅ Archive listing passed"
    else
        echo "❌ Archive listing failed"
    fi
else
    echo "⚠️ Skipping archive listing (no archive path available)"
fi

# Test 7: Decompress Archive
echo ""
echo "📦 Test 7: Decompress Archive"
echo "----------------------------"
if [ -n "$OUTPUT_PATH" ]; then
    DECOMPRESS_RESPONSE=$(curl -s -X POST "$API_BASE/api/uncompress" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "archive_path=$OUTPUT_PATH")
    echo "Response: $DECOMPRESS_RESPONSE"
    
    if echo "$DECOMPRESS_RESPONSE" | grep -q '"status":"success"'; then
        echo "✅ Decompression passed"
        RESTORED_PATH=$(echo "$DECOMPRESS_RESPONSE" | grep -o '"/tmp/compression_api/outputs/restored/[^"]*"' | tr -d '"')
        echo "📁 Files restored to: $RESTORED_PATH"
    else
        echo "❌ Decompression failed"
        exit 1
    fi
else
    echo "⚠️ Skipping decompression (no archive path available)"
fi

# Test 8: Error Handling - Non-existent File
echo ""
echo "🚨 Test 8: Error Handling - Non-existent File"
echo "--------------------------------------------"
ERROR_RESPONSE=$(curl -s -X POST "$API_BASE/api/compress" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "input_path=/nonexistent/file.txt")
echo "Response: $ERROR_RESPONSE"

if echo "$ERROR_RESPONSE" | grep -q '"detail".*"not found"' || echo "$ERROR_RESPONSE" | grep -q '404' || echo "$ERROR_RESPONSE" | grep -q '"detail"'; then
    echo "✅ Error handling passed"
else
    echo "❌ Error handling failed"
fi

# Test 9: Error Handling - No Input
echo ""
echo "🚨 Test 9: Error Handling - No Input"
echo "-----------------------------------"
NO_INPUT_RESPONSE=$(curl -s -X POST "$API_BASE/api/compress")
echo "Response: $NO_INPUT_RESPONSE"

if echo "$NO_INPUT_RESPONSE" | grep -q '"detail"' || echo "$NO_INPUT_RESPONSE" | grep -q '400'; then
    echo "✅ No input error handling passed"
else
    echo "❌ No input error handling failed"
fi

# Cleanup
echo ""
echo "🧹 Cleaning up test files..."
rm -f "$TEST_FILE"
rm -rf "$TEST_DIR"
echo "✅ Cleanup completed"

echo ""
echo "🎉 All tests completed successfully!"
echo "=================================="
echo ""
echo "📊 Test Summary:"
echo "✅ Health Check"
echo "✅ File Upload Compression"
echo "✅ Local File Compression"
echo "✅ Directory Compression"
echo "✅ Maximum Compression (-m5)"
echo "✅ Archive Listing"
echo "✅ Archive Decompression"
echo "✅ Error Handling"
echo ""
echo "🚀 Your ZMT Compression API is working perfectly!"
