"""
FastAPI application for the Lossless Compression API - Phase 1
"""
import logging
import logging.config
import tempfile
import shutil
from pathlib import Path
from typing import List, Optional, Union
import os

from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import <PERSON><PERSON><PERSON>esponse, FileResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import uvicorn

from config import (
    API_HOST, API_PORT, API_TITLE, API_VERSION, API_DESCRIPTION,
    MAX_FILE_SIZE, MAX_TOTAL_SIZE, ensure_directories, LOGGING_CONFIG
)
from compression import compress_file, compress_multiple_files, CompressionError
from uncompression import decompress_archive, list_archive_contents, DecompressionError

# Configure logging
logging.config.dictConfig(LOGGING_CONFIG)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title=API_TITLE,
    version=API_VERSION,
    description=API_DESCRIPTION
)

# Mount static files for GUI
app.mount("/static", StaticFiles(directory="gui"), name="static")

# Pydantic models for request/response
class CompressionRequest(BaseModel):
    input_path: str
    output_filename: Optional[str] = None

class DecompressionRequest(BaseModel):
    archive_path: str
    output_dir: Optional[str] = None

class StatusResponse(BaseModel):
    status: str
    message: str

@app.on_event("startup")
async def startup_event():
    """Initialize the application"""
    ensure_directories()
    logger.info("Compression API started successfully")

@app.get("/")
async def serve_gui():
    """Serve the GUI interface"""
    return FileResponse("gui/index.html")

@app.get("/api/status", response_model=StatusResponse)
async def get_status():
    """Check API health status"""
    return StatusResponse(
        status="ok",
        message="Compression API is running"
    )

@app.post("/api/compress")
async def compress_endpoint(
    files: List[UploadFile] = File(default=[]),
    input_path: Optional[str] = Form(default=None),
    output_filename: Optional[str] = Form(default=None),
    maxCompress: Optional[bool] = Form(default=False),
    deleteOriginal: Optional[bool] = Form(default=False)
):
    """
    Compress files or directories

    Two modes:
    1. Upload files via multipart/form-data
    2. Specify local path via JSON or form data

    Parameters:
    - files: List of files to upload and compress
    - input_path: Local file or directory path to compress
    - output_filename: Custom output filename (optional)
    - maxCompress: Enable maximum compression with -m5 flag (optional, default: False)
    - deleteOriginal: Delete original files after successful compression (optional, default: False)
    """
    try:
        # Mode 1: File uploads
        if files and len(files) > 0:
            # Validate uploaded files
            total_size = 0
            for file in files:
                if file.size and file.size > MAX_FILE_SIZE:
                    raise HTTPException(
                        status_code=413,
                        detail=f"File {file.filename} exceeds maximum size limit"
                    )
                total_size += file.size or 0
            
            if total_size > MAX_TOTAL_SIZE:
                raise HTTPException(
                    status_code=413,
                    detail="Total upload size exceeds limit"
                )
            
            # Save uploaded files to temporary directory
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                saved_files = []
                
                for file in files:
                    if file.filename:
                        file_path = temp_path / file.filename
                        with open(file_path, "wb") as buffer:
                            shutil.copyfileobj(file.file, buffer)
                        saved_files.append(str(file_path))
                
                # Compress the files
                # Note: For uploaded files, deleteOriginal doesn't apply since they're in temp directories
                if len(saved_files) == 1:
                    result = compress_file(saved_files[0], output_filename, maxCompress, delete_original=False)
                else:
                    result = compress_multiple_files(saved_files, output_filename, maxCompress, delete_original=False)
                
                return JSONResponse(content=result)
        
        # Mode 2: Local path compression
        elif input_path:
            try:
                if not os.path.exists(input_path):
                    raise HTTPException(
                        status_code=404,
                        detail=f"Input path not found: {input_path}"
                    )

                result = compress_file(input_path, output_filename, maxCompress, deleteOriginal)
                return JSONResponse(content=result)
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error compressing local path {input_path}: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Compression failed: {str(e)}")
        
        else:
            raise HTTPException(
                status_code=400,
                detail="Either upload files or provide input_path"
            )
    
    except HTTPException:
        raise
    except CompressionError as e:
        logger.error(f"Compression error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in compress endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/api/uncompress")
async def uncompress_endpoint(
    archive_file: Optional[UploadFile] = File(default=None),
    archive_path: Optional[str] = Form(default=None),
    output_dir: Optional[str] = Form(default=None)
):
    """
    Decompress archive files
    
    Two modes:
    1. Upload archive via multipart/form-data
    2. Specify local archive path
    """
    try:
        # Mode 1: Archive upload
        if archive_file:
            if archive_file.size and archive_file.size > MAX_FILE_SIZE:
                raise HTTPException(
                    status_code=413,
                    detail="Archive file exceeds maximum size limit"
                )
            
            # Save uploaded archive to temporary location
            with tempfile.NamedTemporaryFile(delete=False, suffix=".zmt") as temp_file:
                shutil.copyfileobj(archive_file.file, temp_file)
                temp_archive_path = temp_file.name
            
            try:
                result = decompress_archive(temp_archive_path, output_dir)
                return JSONResponse(content=result)
            finally:
                # Clean up temporary file
                os.unlink(temp_archive_path)
        
        # Mode 2: Local archive path
        elif archive_path:
            try:
                if not os.path.exists(archive_path):
                    raise HTTPException(
                        status_code=404,
                        detail=f"Archive file not found: {archive_path}"
                    )

                result = decompress_archive(archive_path, output_dir)
                return JSONResponse(content=result)
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error decompressing archive {archive_path}: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Decompression failed: {str(e)}")
        
        else:
            raise HTTPException(
                status_code=400,
                detail="Either upload archive file or provide archive_path"
            )
    
    except HTTPException:
        raise
    except DecompressionError as e:
        logger.error(f"Decompression error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in uncompress endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/api/archive/contents")
async def list_archive_contents_endpoint(archive_path: str):
    """List contents of an archive without extracting it"""
    try:
        if not os.path.exists(archive_path):
            raise HTTPException(
                status_code=404,
                detail=f"Archive file not found: {archive_path}"
            )
        
        result = list_archive_contents(archive_path)
        return JSONResponse(content=result)
    
    except DecompressionError as e:
        logger.error(f"Archive listing error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in archive contents endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=API_HOST,
        port=API_PORT,
        reload=True,
        log_config=LOGGING_CONFIG
    )
