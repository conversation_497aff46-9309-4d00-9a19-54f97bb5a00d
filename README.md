# Lossless Compression API - Phase 1

A FastAPI-based REST API for lossless file and directory compression using a custom shell script wrapper.

## 🚀 Features

- **File Compression**: Compress single files or multiple files
- **Directory Compression**: Compress entire directories with full folder support
- **Maximum Compression**: Optional `-m5` flag for maximum compression ratios
- **File Upload Support**: Upload files via multipart/form-data
- **Local Path Support**: Compress files/directories by specifying local paths
- **Decompression**: Restore compressed .zmt archives
- **Archive Inspection**: List contents of archives without extracting
- **Comprehensive Error Handling**: Proper HTTP status codes and error messages
- **Size Statistics**: Get compression ratios and file sizes

## 📁 Project Structure

```
compression-api/
├── main.py                     # FastAPI application entry point
├── compression.py              # Compression logic wrapper
├── uncompression.py            # Decompression logic wrapper
├── config.py                   # Configuration settings
├── zmt.sh                      # ZMT compression shell script wrapper
├── zmt                         # ZMT compression binary
├── requirements.txt            # Python dependencies
├── tests/
│   ├── __init__.py
│   └── test_local.py           # Python test suite
├── postman_collection.json     # Postman API collection
├── postman_environment.json    # Postman environment variables
├── test_api_with_curl.sh       # Command-line test script
├── POSTMAN_DOCS.md            # Postman documentation
├── POSTMAN_SETUP.md           # Postman setup guide
└── README.md                  # This file
```

## 🛠️ Installation

1. **Install dependencies**:
   ```bash
   python3 -m pip install -r requirements.txt
   ```

2. **Make the compression script executable**:
   ```bash
   chmod +x zmt.sh
   ```

## 🏃 Running the API

Start the development server:

```bash
python3 main.py
```

The API will be available at `http://localhost:8000`

## 📚 API Endpoints

### 1. Health Check
```bash
GET /api/status
```

**Response:**
```json
{
  "status": "ok",
  "message": "Compression API is running"
}
```

### 2. Compress Files

#### Upload Files
```bash
curl -X POST http://localhost:8000/api/compress \
  -F "files=@/path/to/file1.txt" \
  -F "files=@/path/to/file2.txt"
```

#### Upload Files with Maximum Compression
```bash
curl -X POST http://localhost:8000/api/compress \
  -F "files=@/path/to/file.txt" \
  -F "maxCompress=true"
```

#### Compress Local Path
```bash
curl -X POST http://localhost:8000/api/compress \
  -d "input_path=/path/to/file/or/directory" \
  -H "Content-Type: application/x-www-form-urlencoded"
```

#### Compress Local Directory with Maximum Compression
```bash
curl -X POST http://localhost:8000/api/compress \
  -d "input_path=/path/to/directory&maxCompress=true" \
  -H "Content-Type: application/x-www-form-urlencoded"
```

**Response:**
```json
{
  "status": "success",
  "output_path": "/tmp/compression_api/outputs/file.zmt",
  "original_size": "180.0MB",
  "compressed_size": "70.0MB",
  "compression_ratio": "61.1%",
  "original_size_bytes": 188743680,
  "compressed_size_bytes": 73400320
}
```

**Parameters:**
- `files`: One or more files to upload and compress
- `input_path`: Local file or directory path to compress
- `output_filename`: Custom output filename (optional)
- `maxCompress`: Enable maximum compression with `-m5` flag (optional, default: false)

### 3. Decompress Archives

#### Upload Archive
```bash
curl -X POST http://localhost:8000/api/uncompress \
  -F "archive_file=@/path/to/archive.zmt"
```

#### Decompress Local Archive
```bash
curl -X POST http://localhost:8000/api/uncompress \
  -d "archive_path=/path/to/archive.zmt" \
  -H "Content-Type: application/x-www-form-urlencoded"
```

**Response:**
```json
{
  "status": "success",
  "restored_path": "/tmp/compression_api/outputs/restored/archive_name",
  "extracted_files": 5,
  "total_size": "180.0MB",
  "total_size_bytes": 188743680
}
```

### 4. List Archive Contents
```bash
curl -X GET "http://localhost:8000/api/archive/contents?archive_path=/path/to/archive.zmt"
```

**Response:**
```json
{
  "status": "success",
  "archive_path": "/path/to/archive.zmt",
  "file_count": 3,
  "contents": ["file1.txt", "file2.txt", "directory/"],
  "truncated": false
}
```

## 🧪 Testing

### Automated Test Suite
Run the Python test suite:

```bash
python3 -m pytest tests/test_local.py -v
```

### Postman API Testing
Complete Postman collection with automated tests:

```bash
# Import postman_collection.json and postman_environment.json into Postman
# See POSTMAN_SETUP.md for detailed instructions
```

### Command Line Testing
Quick API validation script:

```bash
./test_api_with_curl.sh
```

## ⚙️ Configuration

Edit `config.py` to customize:

- **File size limits**: `MAX_FILE_SIZE`, `MAX_TOTAL_SIZE`
- **Output directories**: `TEMP_DIR`, `OUTPUT_DIR`
- **Compression script path**: `COMPRESSION_SCRIPT`
- **File extension**: `COMPRESSION_EXTENSION`

## 🔧 Compression Script

The `zmt.sh` uses tar with gzip compression. For production, replace this with your custom lossless compression algorithm.

## 📝 Error Handling

The API returns appropriate HTTP status codes:

- **200**: Success
- **400**: Bad request (missing parameters)
- **404**: File/archive not found
- **413**: File size exceeds limits
- **422**: Validation error
- **500**: Internal server error

## 🚀 Next Steps (Phase 2)

- Dockerization
- Cloud deployment
- Enhanced compression algorithms
- Authentication and rate limiting
- Async processing for large files

## 📄 License

This project is part of Phase 1 development for the Lossless Compression API Service.
