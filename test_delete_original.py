#!/usr/bin/env python3
"""
Test script to demonstrate the deleteOriginal functionality
"""
import os
import tempfile
import requests
import json
from pathlib import Path

def test_delete_original_api():
    """Test the deleteOriginal flag via API"""
    
    # Create a test file
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
        test_file_path = f.name
        f.write("This is a test file for compression with deleteOriginal=True\n" * 100)
    
    print(f"Created test file: {test_file_path}")
    print(f"File exists before compression: {os.path.exists(test_file_path)}")
    
    # Test compression with deleteOriginal=True
    try:
        response = requests.post(
            "http://localhost:8000/api/compress",
            data={
                "input_path": test_file_path,
                "deleteOriginal": True,
                "maxCompress": False
            }
        )
        
        if response.status_code == 200:
            result = response.json()
            print("\nCompression successful!")
            print(f"Status: {result.get('status')}")
            print(f"Output path: {result.get('output_path')}")
            print(f"Original deleted: {result.get('original_deleted', 'Not specified')}")
            print(f"File exists after compression: {os.path.exists(test_file_path)}")
            
            if result.get('deletion_error'):
                print(f"Deletion error: {result.get('deletion_error')}")
        else:
            print(f"API request failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("Could not connect to API. Make sure the server is running on localhost:8000")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Clean up if file still exists
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
            print(f"Cleaned up test file: {test_file_path}")

def test_delete_original_false():
    """Test with deleteOriginal=False to ensure file is preserved"""
    
    # Create a test file
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
        test_file_path = f.name
        f.write("This is a test file for compression with deleteOriginal=False\n" * 50)
    
    print(f"\nCreated test file: {test_file_path}")
    print(f"File exists before compression: {os.path.exists(test_file_path)}")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/compress",
            data={
                "input_path": test_file_path,
                "deleteOriginal": False,
                "maxCompress": False
            }
        )
        
        if response.status_code == 200:
            result = response.json()
            print("\nCompression successful!")
            print(f"Status: {result.get('status')}")
            print(f"Output path: {result.get('output_path')}")
            print(f"Original deleted: {result.get('original_deleted', 'Not specified')}")
            print(f"File exists after compression: {os.path.exists(test_file_path)}")
        else:
            print(f"API request failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("Could not connect to API. Make sure the server is running on localhost:8000")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Clean up
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
            print(f"Cleaned up test file: {test_file_path}")

if __name__ == "__main__":
    print("Testing deleteOriginal functionality...")
    print("=" * 50)
    
    print("Test 1: deleteOriginal=True (should delete original file)")
    test_delete_original_api()
    
    print("\n" + "=" * 50)
    print("Test 2: deleteOriginal=False (should preserve original file)")
    test_delete_original_false()
    
    print("\n" + "=" * 50)
    print("Tests completed!")
