"""
Local tests for the Compression API
"""
import pytest
import tempfile
import os
from pathlib import Path
from fastapi.testclient import TestClient
import json

from main import app
from config import ensure_directories

client = TestClient(app)

@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Set up test environment"""
    ensure_directories()

@pytest.fixture
def sample_text_file():
    """Create a sample text file for testing"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write("This is a test file for compression.\n" * 100)
        f.write("It contains some repeated text to test compression ratios.\n" * 50)
        return f.name

@pytest.fixture
def sample_directory():
    """Create a sample directory with multiple files"""
    temp_dir = tempfile.mkdtemp()
    
    # Create multiple files in the directory
    for i in range(3):
        file_path = Path(temp_dir) / f"test_file_{i}.txt"
        with open(file_path, 'w') as f:
            f.write(f"This is test file number {i}.\n" * 20)
    
    return temp_dir

def test_status_endpoint():
    """Test the status endpoint"""
    response = client.get("/api/status")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ok"
    assert "message" in data

def test_compress_local_file(sample_text_file):
    """Test compressing a local file via path"""
    response = client.post(
        "/api/compress",
        data={"input_path": sample_text_file}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "output_path" in data
    assert "original_size" in data
    assert "compressed_size" in data
    assert "compression_ratio" in data
    
    # Verify output file exists
    output_path = data["output_path"]
    assert os.path.exists(output_path)
    
    # Clean up
    os.unlink(sample_text_file)
    if os.path.exists(output_path):
        os.unlink(output_path)

def test_compress_directory(sample_directory):
    """Test compressing a directory"""
    response = client.post(
        "/api/compress",
        data={"input_path": sample_directory}
    )

    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "output_path" in data

    # Verify output file exists
    output_path = data["output_path"]
    assert os.path.exists(output_path)

    # Clean up
    import shutil
    shutil.rmtree(sample_directory)
    if os.path.exists(output_path):
        os.unlink(output_path)

def test_compress_directory_max_compression(sample_directory):
    """Test compressing a directory with maximum compression"""
    response = client.post(
        "/api/compress",
        data={"input_path": sample_directory, "maxCompress": "true"}
    )

    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "output_path" in data

    # Verify output file exists
    output_path = data["output_path"]
    assert os.path.exists(output_path)

    # Clean up
    import shutil
    shutil.rmtree(sample_directory)
    if os.path.exists(output_path):
        os.unlink(output_path)

def test_compress_file_upload():
    """Test compressing via file upload"""
    # Create a temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write("Test content for upload compression.\n" * 50)
        temp_file = f.name

    try:
        files_to_upload = [("files", ("test.txt", open(temp_file, 'rb'), "text/plain"))]
        response = client.post(
            "/api/compress",
            files=files_to_upload
        )

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "output_path" in data

        # Verify output file exists
        output_path = data["output_path"]
        assert os.path.exists(output_path)

        # Clean up output file
        if os.path.exists(output_path):
            os.unlink(output_path)

    finally:
        # Clean up input file
        os.unlink(temp_file)

def test_compress_file_upload_max_compression():
    """Test compressing via file upload with maximum compression"""
    # Create a temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write("Test content for max compression.\n" * 100)
        temp_file = f.name

    try:
        files_to_upload = [("files", ("test.txt", open(temp_file, 'rb'), "text/plain"))]
        response = client.post(
            "/api/compress",
            files=files_to_upload,
            data={"maxCompress": "true"}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "output_path" in data

        # Verify output file exists
        output_path = data["output_path"]
        assert os.path.exists(output_path)

        # Clean up output file
        if os.path.exists(output_path):
            os.unlink(output_path)

    finally:
        # Clean up input file
        os.unlink(temp_file)

def test_uncompress_archive():
    """Test decompressing an archive"""
    # First create an archive by compressing a file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write("Test content for decompression test.\n" * 30)
        temp_file = f.name
    
    try:
        # Compress the file
        compress_response = client.post(
            "/api/compress",
            data={"input_path": temp_file}
        )
        
        assert compress_response.status_code == 200
        compress_data = compress_response.json()
        archive_path = compress_data["output_path"]
        
        # Now decompress it
        decompress_response = client.post(
            "/api/uncompress",
            data={"archive_path": archive_path}
        )
        
        assert decompress_response.status_code == 200
        decompress_data = decompress_response.json()
        assert decompress_data["status"] == "success"
        assert "restored_path" in decompress_data
        
        # Verify restored directory exists
        restored_path = decompress_data["restored_path"]
        assert os.path.exists(restored_path)
        
        # Clean up
        if os.path.exists(archive_path):
            os.unlink(archive_path)
        if os.path.exists(restored_path):
            import shutil
            shutil.rmtree(restored_path)
            
    finally:
        os.unlink(temp_file)

def test_compress_nonexistent_file():
    """Test error handling for non-existent file"""
    response = client.post(
        "/api/compress",
        data={"input_path": "/nonexistent/file.txt"}
    )
    
    assert response.status_code == 404

def test_uncompress_nonexistent_archive():
    """Test error handling for non-existent archive"""
    response = client.post(
        "/api/uncompress",
        data={"archive_path": "/nonexistent/archive.zmt"}
    )
    
    assert response.status_code == 404

def test_compress_no_input():
    """Test error handling when no input is provided"""
    response = client.post("/api/compress")
    
    assert response.status_code == 400

def test_uncompress_no_input():
    """Test error handling when no archive is provided"""
    response = client.post("/api/uncompress")
    
    assert response.status_code == 400

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
