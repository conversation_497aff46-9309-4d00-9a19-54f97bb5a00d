# 🚀 Postman Setup Guide for ZMT Compression API

## 📦 What's Included

Your Postman documentation package includes:

- **`postman_collection.json`** - Complete API collection with all endpoints
- **`postman_environment.json`** - Environment variables and configuration
- **`POSTMAN_DOCS.md`** - Comprehensive documentation
- **`test_api_with_curl.sh`** - Command-line test script
- **`POSTMAN_SETUP.md`** - This setup guide

## 🛠️ Quick Setup (5 minutes)

### Step 1: Import into Postman

1. **Open Postman** (Download from [postman.com](https://www.postman.com/) if needed)

2. **Import Collection:**
   ```
   File → Import → Upload Files → Select "postman_collection.json"
   ```

3. **Import Environment:**
   ```
   File → Import → Upload Files → Select "postman_environment.json"
   ```

4. **Activate Environment:**
   ```
   Top-right dropdown → Select "ZMT Compression API Environment"
   ```

### Step 2: Start Your API

```bash
cd /path/to/compression-api
python3 main.py
```

### Step 3: Test the Collection

1. **Quick Test:**
   - Click "Collections" → "ZMT Compression API" → "Health Check" → "Get API Status"
   - Click "Send"
   - Should return: `{"status": "ok", "message": "Compression API is running"}`

2. **Run All Tests:**
   - Click "Collections" → "ZMT Compression API" → "Run"
   - Click "Run ZMT Compression API"
   - Watch all tests execute automatically

## 📋 Collection Overview

### 🏥 Health Check
- **Get API Status** - Verify API is running

### 🗜️ Compression Endpoints
- **Compress File Upload** - Upload files via form-data
- **Compress Local Path** - Compress files/directories by path

### 📦 Decompression Endpoints
- **Decompress Archive Upload** - Upload .zmt files to extract
- **Decompress Local Archive** - Extract archives by file path

### 📋 Archive Management
- **List Archive Contents** - Get archive information

## 🔧 Environment Variables

| Variable | Purpose | Example Value |
|----------|---------|---------------|
| `base_url` | API endpoint | `http://localhost:8000` |
| `last_compressed_file` | Auto-populated by tests | `/tmp/compression_api/outputs/file.zmt` |
| `sample_file_path` | Your test file | `/Users/<USER>/Documents/test.txt` |

## 📝 How to Use Each Endpoint

### 1. File Upload Compression

```
POST /api/compress
Body: form-data
- files: [Select your file(s)]
- output_filename: "my_archive.zmt" (optional)
```

**Steps:**
1. Go to "Compression" → "Compress File Upload"
2. In Body tab → form-data → files → Select File
3. Choose any file from your computer
4. Click "Send"

### 2. Local Path Compression

```
POST /api/compress
Body: x-www-form-urlencoded
- input_path: "/full/path/to/file.txt"
```

**Steps:**
1. Go to "Compression" → "Compress Local Path"
2. Update `input_path` with a real file path on your system
3. Click "Send"

### 3. Archive Decompression

```
POST /api/uncompress
Body: x-www-form-urlencoded
- archive_path: "/path/to/archive.zmt"
```

**Steps:**
1. First run a compression request to create an archive
2. Go to "Decompression" → "Decompress Local Archive"
3. The `{{last_compressed_file}}` variable will be auto-populated
4. Click "Send"

## 🧪 Automated Testing Features

Each request includes automated tests that check:

- ✅ HTTP status codes (200, 404, 500, etc.)
- ✅ Response structure validation
- ✅ Required fields presence
- ✅ Data type validation
- ✅ Environment variable updates

### Test Results Example:
```
✓ Status code is 200
✓ Response has compression data
✓ Output path is valid
✓ Compression ratio is calculated
```

## 🚨 Troubleshooting

### Common Issues:

1. **"Connection refused"**
   - ✅ Make sure API server is running: `python3 main.py`
   - ✅ Check URL is `http://localhost:8000`

2. **"File not found" errors**
   - ✅ Update `sample_file_path` in environment with real file paths
   - ✅ Use absolute paths (e.g., `/Users/<USER>/Documents/file.txt`)

3. **"No files uploaded" error**
   - ✅ In file upload requests, make sure to select actual files
   - ✅ Check the "files" field in form-data has a file selected

4. **Tests failing**
   - ✅ Run requests individually first
   - ✅ Check environment variables are set correctly
   - ✅ Verify API server logs for detailed errors

### Debug Tips:

- **View Console:** View → Show Postman Console (see detailed logs)
- **Check Environment:** Click the eye icon next to environment name
- **Verify Paths:** Make sure file paths exist on your system
- **API Logs:** Check your terminal running the API for error details

## 🎯 Pro Tips

### 1. Custom File Testing
Update environment variables with your own test files:
```json
{
  "sample_file_path": "/Users/<USER>/Documents/your-test-file.pdf",
  "sample_directory_path": "/Users/<USER>/Documents/test-folder"
}
```

### 2. Workflow Testing
Run requests in this order for complete workflow:
1. Health Check
2. Compress File Upload
3. List Archive Contents  
4. Decompress Local Archive

### 3. Performance Testing
- Use Collection Runner with multiple iterations
- Monitor response times
- Test with different file sizes

### 4. Custom Tests
Add your own test scripts in the "Tests" tab:
```javascript
pm.test("Compression ratio is good", function () {
    const jsonData = pm.response.json();
    const ratio = parseFloat(jsonData.compression_ratio);
    pm.expect(ratio).to.be.above(0);
});
```

## 🚀 Alternative Testing

If you prefer command-line testing, use the included script:

```bash
# Make sure API is running first
python3 main.py

# In another terminal:
./test_api_with_curl.sh
```

This will run a complete test suite using curl commands.

---

**You're all set! Start compressing files like a boss! 🎉**
